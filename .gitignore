# CoHost.AI - Git Ignore File

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDEs and editors
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
# Credentials and sensitive files (NEVER COMMIT THESE!)
*credentials*.json
*service-account*.json
mike-*.json
google-*.json
.env
.env.local
.env.production

# Keep example files
!.env.example
!requirements.txt
!setup.py

# Logs
*.log
voice_assistant.log*

# History and cache files
cohost_history.json
history/
*.cache

# Audio test files
*.wav
*.mp3
*.ogg

# Temporary files
tmp/
temp/
*.tmp

# Documentation builds
docs/_build/
site/

# Backup files
*.bak
*.backup
*~

# Local configuration overrides
.env.local
config.local.py

# Performance test results
benchmark_results/
performance_logs/
