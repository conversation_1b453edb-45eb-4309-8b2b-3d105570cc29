#!/usr/bin/env python3
"""
Setup script for CoHost.AI

This script helps with initial setup and dependency installation.
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors."""
    print(f"🔄 {description}...")
    try:
        # Use subprocess.run with proper shell handling for Windows
        subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        if e.stdout:
            print(f"STDOUT: {e.stdout}")
        if e.stderr:
            print(f"STDERR: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible."""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"❌ Python 3.8+ required, but you have {version.major}.{version.minor}")
        return False
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")
    return True

def check_ollama():
    """Check if Ollama is installed and running."""
    print("🔍 Checking Ollama installation...")
    
    # Check if ollama command exists
    if not shutil.which("ollama"):
        print("❌ Ollama not found. Please install from https://ollama.ai")
        return False
    
    print("✅ Ollama is installed")
    
    # Check if Ollama is running
    try:
        result = subprocess.run(["ollama", "list"], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ Ollama is running")

            # Check if mistral model is available
            if "mistral" in result.stdout:
                print("✅ Mistral model is available")
            else:
                print("⚠️  Mistral model not found. Installing...")
                try:
                    subprocess.run(["ollama", "pull", "mistral"], check=True, capture_output=True, text=True)
                    print("✅ Mistral model installed")
                except subprocess.CalledProcessError as e:
                    print("❌ Failed to install Mistral model")
                    print(f"Error: {e}")
                    return False
            return True
        else:
            print("❌ Ollama is not running. Please start it with: ollama serve")
            return False
    except subprocess.TimeoutExpired:
        print("❌ Ollama command timed out. Please check if it's running properly.")
        return False
    except Exception as e:
        print(f"❌ Error checking Ollama: {e}")
        return False

def create_env_file():
    """Create .env file from template if it doesn't exist."""
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if env_file.exists():
        print("✅ .env file already exists")
        return True
    
    if not env_example.exists():
        print("❌ .env.example template not found")
        return False
    
    try:
        shutil.copy(env_example, env_file)
        print("✅ Created .env file from template")
        print("⚠️  Please edit .env file with your actual configuration values")
        return True
    except Exception as e:
        print(f"❌ Failed to create .env file: {e}")
        return False

def run_pip_command(args, description):
    """Run a pip command with proper argument handling."""
    print(f"🔄 {description}...")
    try:
        # Use subprocess.run with argument list to handle spaces properly
        cmd = [sys.executable, "-m", "pip"] + args
        subprocess.run(cmd, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        if e.stdout:
            print(f"STDOUT: {e.stdout}")
        if e.stderr:
            print(f"STDERR: {e.stderr}")
        return False

def install_dependencies():
    """Install Python dependencies."""
    if not run_pip_command(["install", "--upgrade", "pip"], "Upgrading pip"):
        return False

    if not run_pip_command(["install", "-r", "requirements.txt"], "Installing dependencies"):
        return False

    return True

def main():
    """Main setup function."""
    print("🚀 CoHost.AI Setup")
    print("=" * 30)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        print("❌ Failed to install dependencies")
        sys.exit(1)
    
    # Check Ollama
    if not check_ollama():
        print("❌ Ollama setup incomplete")
        print("Please install Ollama from https://ollama.ai and run 'ollama serve'")
        sys.exit(1)
    
    # Create .env file
    if not create_env_file():
        print("❌ Failed to create .env file")
        sys.exit(1)
    
    print("\n🎉 Setup completed successfully!")
    print("\nNext steps:")
    print("1. Edit .env file with your configuration")
    print("2. Ensure OBS is running with WebSocket enabled")
    print("3. Configure Streamer.bot to send UDP broadcasts")
    print("4. Run: python run.py")
    print("\nFor audio device configuration, run: python test.py")

if __name__ == "__main__":
    main()
