@echo off
echo CoHost.AI Setup
echo ===============

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8+ from https://python.org
    pause
    exit /b 1
)

REM Change to the script directory to handle spaces in path
cd /d "%~dp0"

REM Run the setup script
python setup.py

echo.
echo Setup complete! Press any key to exit...
pause >nul
