#!/usr/bin/env python3
"""
TTS Test Script for CoHost.AI

This script tests the TTS functionality independently to help debug issues.
"""

import sys
import os
import logging

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Import with proper module path
from src.tts_manager import TTSManager

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_tts():
    """Test TTS functionality."""
    
    # Check if credentials file exists
    credentials_path = "mike-442823-9d2a2fbbe9a4.json"
    if not os.path.exists(credentials_path):
        print(f"ERROR: Google Cloud credentials file not found: {credentials_path}")
        print("Please make sure the file exists in the current directory.")
        return False
    
    try:
        print("Initializing TTS Manager...")
        tts_manager = TTSManager(
            json_path=credentials_path,
            device_index=7,  # Using the recommended device from test.py
            cache_enabled=False  # Disable cache for testing
        )
        
        print("Testing TTS synthesis and playback...")
        test_text = "Hello! This is a test of the text-to-speech system. Can you hear me?"
        
        print(f"Synthesizing: {test_text}")
        tts_manager.synthesize_and_play(test_text)
        
        print("TTS test completed successfully!")
        return True
        
    except Exception as e:
        print(f"ERROR: TTS test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        try:
            tts_manager.cleanup()
        except:
            pass

if __name__ == "__main__":
    print("=" * 60)
    print("TTS TEST SCRIPT")
    print("=" * 60)
    
    success = test_tts()
    
    print("=" * 60)
    if success:
        print("✓ TTS test PASSED")
    else:
        print("✗ TTS test FAILED")
    print("=" * 60)
