#!/usr/bin/env python3
"""
Audio Device Test Script for CoHost.AI

This script helps you identify the correct audio device index for TTS playback.
Run this script and note the index of your desired output device.
"""

import sys

try:
    import pyaudio
except ImportError:
    print("ERROR: pyaudio not installed. Please install it with:")
    print("pip install pyaudio")
    sys.exit(1)

def list_audio_devices():
    """List all available audio devices with their indices."""
    p = pyaudio.PyAudio()

    print("=" * 80)
    print("AUDIO DEVICES AVAILABLE")
    print("=" * 80)
    print()

    output_devices = []

    for i in range(p.get_device_count()):
        try:
            info = p.get_device_info_by_index(i)
            device_type = []

            if info['maxInputChannels'] > 0:
                device_type.append("INPUT")
            if info['maxOutputChannels'] > 0:
                device_type.append("OUTPUT")
                output_devices.append(i)

            type_str = "/".join(device_type) if device_type else "UNKNOWN"

            print(f"Index: {info['index']:2d} | {type_str:12s} | {info['name']}")
            print(f"           | Channels: In={info['maxInputChannels']:2d}, Out={info['maxOutputChannels']:2d} | Rate: {int(info['defaultSampleRate'])} Hz")
            print()

        except Exception as e:
            print(f"Index: {i:2d} | ERROR: {e}")
            print()

    p.terminate()

    print("=" * 80)
    print("RECOMMENDED OUTPUT DEVICES FOR TTS:")
    print("=" * 80)

    input_devices = []
    recommended_output = []

    if output_devices:
        for idx in output_devices:
            try:
                info = p.get_device_info_by_index(idx)
                if info['maxOutputChannels'] >= 2:  # Stereo or more
                    recommended_output.append((idx, info['name']))
                    print(f"Index {idx}: {info['name']}")
            except Exception:
                continue  # Skip invalid device indices

    if not recommended_output:
        print("No suitable output devices found!")

    print()
    print("=" * 80)
    print("RECOMMENDED INPUT DEVICES FOR MICROPHONE:")
    print("=" * 80)

    for i in range(p.get_device_count()):
        try:
            info = p.get_device_info_by_index(i)
            if info['maxInputChannels'] > 0:
                input_devices.append((i, info['name']))
                print(f"Index {i}: {info['name']}")
        except Exception:
            continue

    if not input_devices:
        print("No suitable input devices found!")

    print()
    print("=" * 80)
    print("CONFIGURATION RECOMMENDATIONS:")
    print("=" * 80)

    # Recommend specific devices based on your GoXLR setup
    print("Based on your TC-Helicon GoXLR setup:")
    print()

    # For TTS output - recommend Chat or System
    chat_outputs = [idx for idx, name in recommended_output if "Chat" in name and "TC-Helicon GoXLR" in name]
    system_outputs = [idx for idx, name in recommended_output if "System" in name and "TC-Helicon GoXLR" in name]

    if chat_outputs:
        print(f"🔊 RECOMMENDED TTS OUTPUT: Index {chat_outputs[0]} (Chat - TC-Helicon GoXLR)")
        print(f"   AUDIO_DEVICE_INDEX={chat_outputs[0]}")
    elif system_outputs:
        print(f"🔊 RECOMMENDED TTS OUTPUT: Index {system_outputs[0]} (System - TC-Helicon GoXLR)")
        print(f"   AUDIO_DEVICE_INDEX={system_outputs[0]}")
    else:
        print("🔊 RECOMMENDED TTS OUTPUT: Index 7 (Chat - TC-Helicon GoXLR)")
        print("   AUDIO_DEVICE_INDEX=7")

    print()

    # For microphone input - recommend Chat Mic
    chat_mics = [idx for idx, name in input_devices if "Chat Mic" in name and "TC-Helicon GoXLR" in name]

    if chat_mics:
        print(f"🎤 RECOMMENDED MICROPHONE: Index {chat_mics[0]} (Chat Mic - TC-Helicon GoXLR)")
        print(f"   MIC_DEVICE_INDEX={chat_mics[0]}")
    else:
        print("🎤 RECOMMENDED MICROPHONE: Index 1 (Chat Mic - TC-Helicon GoXLR)")
        print("   MIC_DEVICE_INDEX=1")

    print()
    print("💡 For streaming setup:")
    print("   • Use Chat output for TTS so viewers can hear Mike")
    print("   • Use Chat Mic input for voice recognition")
    print("   • This keeps Mike separate from your game/music audio")

def test_audio_device(device_index):
    """Test audio playback on a specific device."""
    try:
        print(f"Testing audio device {device_index}...")

        # Verify the device can be opened
        p = pyaudio.PyAudio()

        # Try to open the device
        stream = p.open(
            format=pyaudio.paInt16,
            channels=2,
            rate=44100,
            output=True,
            output_device_index=device_index
        )

        print(f"✓ Device {device_index} opened successfully!")

        stream.close()
        p.terminate()

    except Exception as e:
        print(f"✗ Error testing device {device_index}: {e}")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        try:
            device_index = int(sys.argv[1])
            test_audio_device(device_index)
        except ValueError:
            print("Usage: python test.py [device_index]")
            print("       python test.py  (to list all devices)")
    else:
        list_audio_devices()
