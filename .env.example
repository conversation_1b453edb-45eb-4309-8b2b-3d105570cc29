# CoHost.AI Configuration
# Copy this file to .env and fill in your actual values

# Google Cloud Text-to-Speech
# Copy credentials.example.json to your-credentials.json and update with your actual values
GOOGLE_CREDENTIALS_PATH=your-credentials.json

# UDP Configuration
UDP_PORT=5005

# OBS WebSocket Configuration
OBS_HOST=localhost
OBS_PORT=4455
OBS_PASSWORD=your_obs_websocket_password

# Audio Configuration
AUDIO_DEVICE_INDEX=7

# OBS Scene and Source Names
OBS_SCENE_NAME=In-Game [OLD]
OBS_BOT_SOURCE=AIBot
OBS_TOP_SOURCE=AITop

# Ollama Configuration
OLLAMA_MODEL=mistral

# Logging Level (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO

# Microphone Configuration
MIC_DEVICE_INDEX=-1
PUSH_TO_TALK_START_KEY=F1
PUSH_TO_TALK_STOP_KEY=F2
SPEECH_RECOGNITION_LANGUAGE=en-US
SPEECH_RECOGNITION_TIMEOUT=5.0

# Performance Configuration
ENABLE_PARALLEL_PROCESSING=true
TTS_CACHE_ENABLED=true
TTS_CACHE_SIZE=50
AUDIO_BUFFER_SIZE=4096

# UI Configuration
SHOW_DETAILED_LOGS=false
CLI_REFRESH_RATE=0.1
