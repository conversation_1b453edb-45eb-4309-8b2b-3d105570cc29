import sys
from dotenv import load_dotenv
from src.config import Config, setup_logging
from src.VoiceAssistant import VoiceAssistant

# Load environment variables
load_dotenv()

def main():
    """Main entry point for the CoHost.AI application."""
    try:
        # Setup logging first
        setup_logging()

        # Load configuration
        config = Config()

        print(f"Starting CoHost.AI with configuration: {config}")

        # Initialize and run CoHost.AI
        assistant = VoiceAssistant(config)
        assistant.run()

    except KeyboardInterrupt:
        print("\nShutting down gracefully...")
    except Exception as e:
        print(f"Fatal error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
