# CoHost.AI - AI-Powered Streaming Co-Host

A sophisticated AI co-host that integrates with Twitch chat via Streamer.bot, generates character-based responses using Ollama, and provides text-to-speech with OBS integration for streaming.

## Features

- **UDP Broadcast Listening**: Receives chat messages from Streamer.bot
- **Speech Recognition**: Push-to-talk microphone input with speech-to-text
- **AI Response Generation**: Uses Ollama (Mistral model) for character-based responses
- **Text-to-Speech**: Google Cloud TTS with high-quality voice synthesis and caching
- **OBS Integration**: Automatic character visibility control during speech
- **Conversation History**: Persistent chat history storage
- **Duplicate Prevention**: Avoids processing duplicate messages
- **Beautiful CLI Interface**: Real-time status dashboard with Rich
- **Performance Optimizations**: TTS caching, parallel processing, optimized audio buffers
- **Comprehensive Logging**: Detailed logging for debugging and monitoring
- **Keyboard Shortcuts**: Configurable push-to-talk keys for voice input

## Character: <PERSON> is a sarcastic, misanthropic Canadian character who reluctantly appears on TompTTV's Twitch stream. He's known for:
- Overly sarcastic and snarky responses
- Extreme sassiness and misanthropy
- Confidence that he's not an AI (but suspicious everyone else might be)
- Using Twitch emotes like "POG" in daily speech
- Creating custom exclamations with profanity
- Short, punchy responses (1-2 paragraphs)

## Prerequisites

1. **Python 3.8+**
2. **OBS Studio** with WebSocket plugin enabled
3. **Ollama** installed and running with Mistral model
4. **Google Cloud TTS** service account credentials
5. **Streamer.bot** configured to send UDP broadcasts

## Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd "CoHost.AI"
   ```

   **Note**: If your directory is named "Mike 2.0" (with a space), you may encounter issues with the setup script. Run `migrate_directory.bat` on Windows to rename it to "CoHost.AI", or manually rename the directory.

2. **Create virtual environment**:
   ```bash
   python -m venv venv
   venv\Scripts\activate  # Windows
   # or
   source venv/bin/activate  # Linux/Mac
   ```

3. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

4. **Setup Ollama**:
   ```bash
   # Install Ollama from https://ollama.ai
   ollama pull mistral
   ```

5. **Configure environment**:
   ```bash
   cp .env.example .env
   # Edit .env with your actual values
   ```

## Configuration

Create a `.env` file with the following variables:

```env
# Google Cloud Text-to-Speech
GOOGLE_CREDENTIALS_PATH=path/to/your/google-credentials.json

# UDP Configuration
UDP_PORT=5005

# OBS WebSocket Configuration
OBS_HOST=localhost
OBS_PORT=4455
OBS_PASSWORD=your_obs_websocket_password

# Audio Configuration
AUDIO_DEVICE_INDEX=7

# OBS Scene and Source Names
OBS_SCENE_NAME=In-Game [OLD]
OBS_BOT_SOURCE=AIBot
OBS_TOP_SOURCE=AITop

# Ollama Configuration
OLLAMA_MODEL=mistral

# Logging Level
LOG_LEVEL=INFO

# Microphone Configuration
MIC_DEVICE_INDEX=-1
PUSH_TO_TALK_START_KEY=F1
PUSH_TO_TALK_STOP_KEY=F2
SPEECH_RECOGNITION_LANGUAGE=en-US
SPEECH_RECOGNITION_TIMEOUT=5.0

# Performance Configuration
ENABLE_PARALLEL_PROCESSING=true
TTS_CACHE_ENABLED=true
TTS_CACHE_SIZE=50
AUDIO_BUFFER_SIZE=4096

# UI Configuration
SHOW_DETAILED_LOGS=false
CLI_REFRESH_RATE=0.1
```

## Usage

1. **Start Ollama** (if not running as service):
   ```bash
   ollama serve
   ```

2. **Start OBS Studio** with WebSocket server enabled

3. **Run the Voice Assistant**:
   ```bash
   python run.py
   ```

4. **Configure Streamer.bot** to send UDP broadcasts to `localhost:5005`

5. **Test your setup** (optional):
   ```bash
   python test.py              # Test audio devices
   python test_speech.py       # Test speech recognition
   python test_performance.py  # Test performance
   ```

## Voice Input Usage

Once running, you can interact with Mike in two ways:

1. **Chat Integration**: Messages from Twitch chat via Streamer.bot
2. **Voice Input**: Use keyboard shortcuts for push-to-talk
   - Press **F1** (or your configured key) to start recording
   - Speak your message
   - Press **F2** (or your configured key) to stop recording
   - Your speech will be converted to text and sent to Mike for response

## Project Structure

```
CoHost.AI/
├── src/
│   ├── VoiceAssistant.py           # Main application logic
│   ├── AiManager.py                # AI response generation (Ollama)
│   ├── OBSWebsocketsManager.py     # OBS integration
│   ├── tts_manager.py              # Text-to-speech handling
│   ├── SpeechRecognitionManager.py # Speech recognition and push-to-talk
│   └── config.py                   # Configuration management
├── run.py                     # Application entry point
├── requirements.txt           # Python dependencies
├── .env.example              # Environment configuration template
├── history.json              # Conversation history (auto-generated)
└── README.md                 # This file
```

## Troubleshooting

### Common Issues

1. **Setup fails with "is not recognized as internal or external command"**
   - This happens when the directory name contains spaces (e.g., "Mike 2.0")
   - **Solution 1**: Run `migrate_directory.bat` to rename to "CoHost.AI"
   - **Solution 2**: Manually rename the directory to remove spaces
   - **Solution 3**: Use the updated `setup.py` which handles spaces better

2. **"Could not connect to OBS"**
   - Ensure OBS is running
   - Check WebSocket server is enabled in OBS
   - Verify connection details in `.env`

3. **"Ollama connection failed"**
   - Ensure Ollama is running: `ollama serve`
   - Check if Mistral model is installed: `ollama list`

4. **"Google credentials not found"**
   - Verify the path in `GOOGLE_CREDENTIALS_PATH`
   - Ensure the service account has TTS permissions

5. **Audio playback issues**
   - Run `python test.py` to list audio devices
   - Update `AUDIO_DEVICE_INDEX` in `.env`

6. **Speech recognition not working**
   - Check microphone permissions
   - Run `python test.py` to find your microphone device index
   - Update `MIC_DEVICE_INDEX` in `.env`
   - Ensure you have an internet connection (uses Google Speech Recognition)

### Logs

Check `voice_assistant.log` for detailed error information.

## Development

### Adding New Features

1. Follow the existing code structure
2. Add proper error handling and logging
3. Update configuration in `config.py` if needed
4. Add tests for new functionality

### Code Style

- Use type hints
- Follow PEP 8
- Add docstrings to all functions
- Use logging instead of print statements

## License

[Add your license here]

## Contributing

[Add contribution guidelines here]
