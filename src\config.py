"""Configuration management for CoHost.AI."""

import os
import logging
from typing import Optional

def setup_logging(level: str = None):
    """Setup logging configuration."""
    log_level = level or os.getenv('LOG_LEVEL', 'INFO')
    
    # Convert string to logging level
    numeric_level = getattr(logging, log_level.upper(), logging.INFO)
    
    logging.basicConfig(
        level=numeric_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('voice_assistant.log')
        ]
    )

class Config:
    """Configuration class for CoHost.AI."""
    
    def __init__(self):
        # Google Cloud TTS
        self.google_credentials_path = os.getenv('GOOGLE_CREDENTIALS_PATH')
        
        # UDP Configuration
        self.udp_port = int(os.getenv('UDP_PORT', '5005'))
        
        # OBS Configuration
        self.obs_host = os.getenv('OBS_HOST', 'localhost')
        self.obs_port = int(os.getenv('OBS_PORT', '4455'))
        self.obs_password = os.getenv('OBS_PASSWORD', 'ZPpGrnxDm1pXYwgS')
        
        # Audio Configuration
        self.audio_device_index = int(os.getenv('AUDIO_DEVICE_INDEX', '7'))

        # Microphone Configuration
        self.mic_device_index = int(os.getenv('MIC_DEVICE_INDEX', '-1'))  # -1 for default
        self.push_to_talk_start_key = os.getenv('PUSH_TO_TALK_START_KEY', 'F1')
        self.push_to_talk_stop_key = os.getenv('PUSH_TO_TALK_STOP_KEY', 'F2')
        self.speech_recognition_language = os.getenv('SPEECH_RECOGNITION_LANGUAGE', 'en-US')
        self.speech_recognition_timeout = float(os.getenv('SPEECH_RECOGNITION_TIMEOUT', '5.0'))

        # OBS Scene and Source Names
        self.obs_scene_name = os.getenv('OBS_SCENE_NAME', 'In-Game [OLD]')
        self.obs_bot_source = os.getenv('OBS_BOT_SOURCE', 'AIBot')
        self.obs_top_source = os.getenv('OBS_TOP_SOURCE', 'AITop')

        # Ollama Configuration
        self.ollama_model = os.getenv('OLLAMA_MODEL', 'mistral')

        # Performance Configuration
        self.enable_parallel_processing = os.getenv('ENABLE_PARALLEL_PROCESSING', 'true').lower() == 'true'
        self.tts_cache_enabled = os.getenv('TTS_CACHE_ENABLED', 'true').lower() == 'true'
        self.tts_cache_size = int(os.getenv('TTS_CACHE_SIZE', '50'))
        self.audio_buffer_size = int(os.getenv('AUDIO_BUFFER_SIZE', '4096'))

        # UI Configuration
        self.show_detailed_logs = os.getenv('SHOW_DETAILED_LOGS', 'false').lower() == 'true'
        self.cli_refresh_rate = float(os.getenv('CLI_REFRESH_RATE', '0.1'))

        # History file
        self.history_file = os.getenv('HISTORY_FILE', 'cohost_history.json')

        # Validate required settings
        self._validate()
    
    def _validate(self):
        """Validate required configuration."""
        if not self.google_credentials_path:
            raise ValueError("GOOGLE_CREDENTIALS_PATH environment variable is required")
        
        if not os.path.exists(self.google_credentials_path):
            raise FileNotFoundError(f"Google credentials file not found: {self.google_credentials_path}")
    
    def __repr__(self):
        return f"Config(udp_port={self.udp_port}, obs_host={self.obs_host}, model={self.ollama_model})"
