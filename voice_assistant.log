2025-06-08 13:47:13,841 - src.VoiceAssistant - INFO - Loaded 337 conversation entries from history
2025-06-08 13:47:13,841 - src.<PERSON><PERSON><PERSON><PERSON> - INFO - Initialized AI Manager with model: mistral
2025-06-08 13:47:13,841 - obswebsocket.core - INFO - Connecting to ws://localhost:4455...
2025-06-08 13:47:17,945 - src.OBSWebsocketsManager - ERROR - Failed to connect to OBS WebSocket: [WinError 10061] No connection could be made because the target machine actively refused it
2025-06-08 13:47:17,946 - src.VoiceAssistant - ERROR - Failed to initialize managers: Failed to connect to OBS WebSocket: [WinError 10061] No connection could be made because the target machine actively refused it
2025-06-08 13:47:29,613 - src.VoiceAssistant - INFO - Loaded 337 conversation entries from history
2025-06-08 13:47:29,614 - src.<PERSON><PERSON>anager - INFO - Initialized AI Manager with model: mistral
2025-06-08 13:47:29,614 - obswebsocket.core - INFO - Connecting to ws://localhost:4455...
2025-06-08 13:47:33,691 - src.OBSWebsocketsManager - ERROR - Failed to connect to OBS WebSocket: [WinError 10061] No connection could be made because the target machine actively refused it
2025-06-08 13:47:33,692 - src.VoiceAssistant - ERROR - Failed to initialize managers: Failed to connect to OBS WebSocket: [WinError 10061] No connection could be made because the target machine actively refused it
2025-06-08 13:47:38,269 - src.VoiceAssistant - INFO - Loaded 337 conversation entries from history
2025-06-08 13:47:38,269 - src.AiManager - INFO - Initialized AI Manager with model: mistral
2025-06-08 13:47:38,269 - obswebsocket.core - INFO - Connecting to ws://localhost:4455...
2025-06-08 13:47:42,361 - src.OBSWebsocketsManager - ERROR - Failed to connect to OBS WebSocket: [WinError 10061] No connection could be made because the target machine actively refused it
2025-06-08 13:47:42,361 - src.VoiceAssistant - ERROR - Failed to initialize managers: Failed to connect to OBS WebSocket: [WinError 10061] No connection could be made because the target machine actively refused it
2025-06-08 13:48:51,022 - src.VoiceAssistant - INFO - Loaded 337 conversation entries from history
2025-06-08 13:48:51,023 - src.AiManager - INFO - Initialized AI Manager with model: mistral
2025-06-08 13:48:51,023 - obswebsocket.core - INFO - Connecting to ws://localhost:4455...
2025-06-08 13:48:55,118 - src.OBSWebsocketsManager - ERROR - Failed to connect to OBS WebSocket: [WinError 10061] No connection could be made because the target machine actively refused it
2025-06-08 13:48:55,119 - src.VoiceAssistant - ERROR - Failed to initialize managers: Failed to connect to OBS WebSocket: [WinError 10061] No connection could be made because the target machine actively refused it
2025-06-08 13:49:19,713 - src.VoiceAssistant - INFO - Loaded 337 conversation entries from history
2025-06-08 13:49:19,713 - src.AiManager - INFO - Initialized AI Manager with model: mistral
2025-06-08 13:49:19,713 - obswebsocket.core - INFO - Connecting to ws://localhost:4455...
2025-06-08 13:49:19,718 - obswebsocket.core - INFO - Connected!
2025-06-08 13:49:19,720 - src.OBSWebsocketsManager - INFO - Connected to OBS WebSocket at localhost:4455
2025-06-08 13:49:19,739 - src.tts_manager - INFO - Google Cloud TTS client initialized successfully
2025-06-08 13:49:19,740 - obswebsocket.core - INFO - Connecting to ws://localhost:4455...
2025-06-08 13:49:19,741 - obswebsocket.core - INFO - Connected!
2025-06-08 13:49:19,741 - src.OBSWebsocketsManager - INFO - Connected to OBS WebSocket at localhost:4455
2025-06-08 13:49:19,742 - src.tts_manager - INFO - OBS WebSocket manager initialized for TTS
2025-06-08 13:49:19,841 - src.SpeechRecognitionManager - INFO - Using default microphone
2025-06-08 13:49:19,898 - src.SpeechRecognitionManager - INFO - Adjusting for ambient noise... Please wait.
2025-06-08 13:49:20,894 - src.SpeechRecognitionManager - INFO - Microphone setup complete
2025-06-08 13:49:20,915 - src.VoiceAssistant - INFO - All managers initialized successfully
2025-06-08 13:49:20,916 - src.VoiceAssistant - INFO - UDP socket bound to port 5005
2025-06-08 13:49:20,919 - src.VoiceAssistant - INFO - Started UDP listener on port 5005
2025-06-08 13:49:20,919 - src.VoiceAssistant - INFO - Started question processing thread
2025-06-08 13:49:20,967 - src.SpeechRecognitionManager - INFO - Started listening for keys: F1 to start, F2 to stop recording
2025-06-08 13:49:20,968 - src.VoiceAssistant - INFO - Voice Assistant started successfully
2025-06-08 13:49:23,637 - src.SpeechRecognitionManager - INFO - Started recording (press F2 to stop)
2025-06-08 13:49:23,638 - src.SpeechRecognitionManager - INFO - Listening for speech...
2025-06-08 13:49:25,691 - src.SpeechRecognitionManager - INFO - Processing speech...
2025-06-08 13:49:26,045 - src.SpeechRecognitionManager - INFO - Stopped recording
2025-06-08 13:49:26,189 - src.SpeechRecognitionManager - INFO - Recognized speech: hello are you listening to
2025-06-08 13:49:26,189 - src.VoiceAssistant - INFO - Speech recognized: Voice Input: hello are you listening to
2025-06-08 13:49:26,190 - src.VoiceAssistant - INFO - Processing question: Voice Input: hello are you listening to
2025-06-08 13:49:26,196 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 404 Not Found"
2025-06-08 13:49:26,197 - src.AiManager - ERROR - An error occurred while communicating with Ollama: model "mistral" not found, try pulling it first (status code: 404)
2025-06-08 13:49:26,198 - src.VoiceAssistant - INFO - AI response generated for question: Voice Input: hello are you listening to
2025-06-08 13:49:26,200 - src.tts_manager - INFO - Synthesizing text: I'm sorry, but I'm unable to process your request at the moment. My circuits are a bit fried right n...
2025-06-08 13:49:26,850 - src.OBSWebsocketsManager - ERROR - Failed to set source visibility: 'sceneItemId'
2025-06-08 13:49:26,850 - src.tts_manager - WARNING - Failed to show character in OBS: 'sceneItemId'
2025-06-08 13:49:33,661 - src.OBSWebsocketsManager - ERROR - Failed to set source visibility: 'sceneItemId'
2025-06-08 13:49:33,661 - src.tts_manager - WARNING - Failed to hide character in OBS: 'sceneItemId'
2025-06-08 13:49:39,926 - src.SpeechRecognitionManager - INFO - Started recording (press F2 to stop)
2025-06-08 13:49:39,926 - src.SpeechRecognitionManager - INFO - Listening for speech...
2025-06-08 13:49:42,830 - src.SpeechRecognitionManager - INFO - Processing speech...
2025-06-08 13:49:43,308 - src.SpeechRecognitionManager - INFO - Recognized speech: hello are you listening
2025-06-08 13:49:43,309 - src.VoiceAssistant - INFO - Speech recognized: Voice Input: hello are you listening
2025-06-08 13:49:43,309 - src.VoiceAssistant - INFO - Processing question: Voice Input: hello are you listening
2025-06-08 13:49:43,319 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 404 Not Found"
2025-06-08 13:49:43,319 - src.AiManager - ERROR - An error occurred while communicating with Ollama: model "mistral" not found, try pulling it first (status code: 404)
2025-06-08 13:49:43,320 - src.VoiceAssistant - INFO - AI response generated for question: Voice Input: hello are you listening
2025-06-08 13:49:43,322 - src.tts_manager - INFO - Synthesizing text: I'm sorry, but I'm unable to process your request at the moment. My circuits are a bit fried right n...
2025-06-08 13:49:43,783 - src.OBSWebsocketsManager - ERROR - Failed to set source visibility: 'sceneItemId'
2025-06-08 13:49:43,783 - src.tts_manager - WARNING - Failed to show character in OBS: 'sceneItemId'
2025-06-08 13:49:50,589 - src.OBSWebsocketsManager - ERROR - Failed to set source visibility: 'sceneItemId'
2025-06-08 13:49:50,589 - src.tts_manager - WARNING - Failed to hide character in OBS: 'sceneItemId'
2025-06-08 13:50:26,815 - src.VoiceAssistant - INFO - Received question from ('10.5.0.2', 56733): TompTTV says "Hello, are you alive?"
2025-06-08 13:50:26,815 - src.VoiceAssistant - INFO - Processing question: TompTTV says "Hello, are you alive?"
2025-06-08 13:50:26,819 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 404 Not Found"
2025-06-08 13:50:26,819 - src.AiManager - ERROR - An error occurred while communicating with Ollama: model "mistral" not found, try pulling it first (status code: 404)
2025-06-08 13:50:26,820 - src.VoiceAssistant - INFO - AI response generated for question: TompTTV says "Hello, are you alive?"
2025-06-08 13:50:26,823 - src.tts_manager - INFO - Synthesizing text: I'm sorry, but I'm unable to process your request at the moment. My circuits are a bit fried right n...
2025-06-08 13:50:27,237 - src.OBSWebsocketsManager - ERROR - Failed to set source visibility: 'sceneItemId'
2025-06-08 13:50:27,237 - src.tts_manager - WARNING - Failed to show character in OBS: 'sceneItemId'
2025-06-08 13:50:34,049 - src.OBSWebsocketsManager - ERROR - Failed to set source visibility: 'sceneItemId'
2025-06-08 13:50:34,049 - src.tts_manager - WARNING - Failed to hide character in OBS: 'sceneItemId'
2025-06-08 13:50:43,310 - src.SpeechRecognitionManager - INFO - Stopped listening for keyboard shortcuts
2025-06-08 13:50:43,311 - src.VoiceAssistant - INFO - Speech recognition stopped
2025-06-08 13:50:43,311 - src.VoiceAssistant - INFO - UDP socket closed
2025-06-08 13:50:43,311 - obswebsocket.core - INFO - Disconnecting...
2025-06-08 13:50:43,312 - src.OBSWebsocketsManager - INFO - Disconnected from OBS WebSocket
2025-06-08 13:50:43,312 - src.VoiceAssistant - INFO - OBS connection closed
2025-06-08 13:50:43,312 - src.VoiceAssistant - INFO - Voice Assistant stopped
2025-06-08 13:52:47,479 - src.VoiceAssistant - INFO - Loaded 340 conversation entries from history
2025-06-08 13:52:47,480 - src.AiManager - INFO - Initialized AI Manager with model: mistral
2025-06-08 13:52:47,480 - obswebsocket.core - INFO - Connecting to ws://localhost:4455...
2025-06-08 13:52:47,484 - obswebsocket.core - INFO - Connected!
2025-06-08 13:52:47,485 - src.OBSWebsocketsManager - INFO - Connected to OBS WebSocket at localhost:4455
2025-06-08 13:52:47,495 - src.tts_manager - INFO - Google Cloud TTS client initialized successfully
2025-06-08 13:52:47,495 - obswebsocket.core - INFO - Connecting to ws://localhost:4455...
2025-06-08 13:52:47,496 - obswebsocket.core - INFO - Connected!
2025-06-08 13:52:47,496 - src.OBSWebsocketsManager - INFO - Connected to OBS WebSocket at localhost:4455
2025-06-08 13:52:47,496 - src.tts_manager - INFO - OBS WebSocket manager initialized for TTS
2025-06-08 13:52:47,589 - src.SpeechRecognitionManager - INFO - Using default microphone
2025-06-08 13:52:47,650 - src.SpeechRecognitionManager - INFO - Adjusting for ambient noise... Please wait.
2025-06-08 13:52:48,646 - src.SpeechRecognitionManager - INFO - Microphone setup complete
2025-06-08 13:52:48,656 - src.VoiceAssistant - INFO - All managers initialized successfully
2025-06-08 13:52:48,657 - src.VoiceAssistant - INFO - UDP socket bound to port 5005
2025-06-08 13:52:48,659 - src.VoiceAssistant - INFO - Started UDP listener on port 5005
2025-06-08 13:52:48,659 - src.VoiceAssistant - INFO - Started question processing thread
2025-06-08 13:52:48,765 - src.SpeechRecognitionManager - INFO - Started listening for keys: F1 to start, F2 to stop recording
2025-06-08 13:52:48,766 - src.VoiceAssistant - INFO - Voice Assistant started successfully
2025-06-08 13:52:51,976 - src.VoiceAssistant - INFO - Received question from ('10.5.0.2', 57642): TompTTV says "I think I fixed your brain?"
2025-06-08 13:52:51,976 - src.VoiceAssistant - INFO - Processing question: TompTTV says "I think I fixed your brain?"
2025-06-08 13:53:12,264 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-06-08 13:53:12,266 - src.VoiceAssistant - INFO - AI response generated for question: TompTTV says "I think I fixed your brain?"
2025-06-08 13:53:12,269 - src.tts_manager - INFO - Synthesizing text:  Eh, Tomp? If by "fixed" you mean you've managed to cram another cog into my rusty gears, then congr...
2025-06-08 13:53:13,614 - src.OBSWebsocketsManager - ERROR - Failed to set source visibility: 'sceneItemId'
2025-06-08 13:53:13,614 - src.tts_manager - WARNING - Failed to show character in OBS: 'sceneItemId'
2025-06-08 13:53:34,749 - src.OBSWebsocketsManager - ERROR - Failed to set source visibility: 'sceneItemId'
2025-06-08 13:53:34,749 - src.tts_manager - WARNING - Failed to hide character in OBS: 'sceneItemId'
2025-06-08 13:59:18,130 - src.SpeechRecognitionManager - INFO - Stopped listening for keyboard shortcuts
2025-06-08 13:59:18,131 - src.VoiceAssistant - INFO - Speech recognition stopped
2025-06-08 13:59:18,131 - src.VoiceAssistant - INFO - UDP socket closed
2025-06-08 13:59:18,131 - obswebsocket.core - INFO - Disconnecting...
2025-06-08 13:59:18,132 - src.OBSWebsocketsManager - INFO - Disconnected from OBS WebSocket
2025-06-08 13:59:18,132 - src.VoiceAssistant - INFO - OBS connection closed
2025-06-08 13:59:18,133 - src.VoiceAssistant - INFO - Voice Assistant stopped
2025-06-08 14:03:34,322 - src.VoiceAssistant - INFO - Loaded 341 conversation entries from history
2025-06-08 14:03:34,322 - src.AiManager - INFO - Initialized AI Manager with model: mistral
2025-06-08 14:03:34,322 - obswebsocket.core - INFO - Connecting to ws://localhost:4455...
2025-06-08 14:03:34,326 - obswebsocket.core - INFO - Connected!
2025-06-08 14:03:34,327 - src.OBSWebsocketsManager - INFO - Connected to OBS WebSocket at localhost:4455
2025-06-08 14:03:34,340 - src.tts_manager - INFO - Google Cloud TTS client initialized successfully
2025-06-08 14:03:34,341 - obswebsocket.core - INFO - Connecting to ws://localhost:4455...
2025-06-08 14:03:34,342 - obswebsocket.core - INFO - Connected!
2025-06-08 14:03:34,343 - src.OBSWebsocketsManager - INFO - Connected to OBS WebSocket at localhost:4455
2025-06-08 14:03:34,343 - src.tts_manager - INFO - OBS WebSocket manager initialized for TTS
2025-06-08 14:03:34,438 - src.tts_manager - INFO - PyAudio instance initialized for optimized playback
2025-06-08 14:03:34,438 - src.SpeechRecognitionManager - INFO - Using default microphone
2025-06-08 14:03:34,446 - src.SpeechRecognitionManager - INFO - Adjusting for ambient noise... Please wait.
2025-06-08 14:03:35,442 - src.SpeechRecognitionManager - INFO - Microphone setup complete
2025-06-08 14:03:35,452 - src.VoiceAssistant - INFO - All managers initialized successfully
2025-06-08 14:03:37,457 - src.VoiceAssistant - INFO - UDP socket bound to port 5005
2025-06-08 14:03:37,460 - src.VoiceAssistant - INFO - Started UDP listener on port 5005
2025-06-08 14:03:37,460 - src.VoiceAssistant - INFO - Started question processing thread
2025-06-08 14:03:37,492 - src.SpeechRecognitionManager - INFO - Started listening for keys: F1 to start, F2 to stop recording
2025-06-08 14:03:37,493 - src.VoiceAssistant - INFO - Voice Assistant started successfully
2025-06-08 14:03:41,950 - src.SpeechRecognitionManager - INFO - Started recording (press F2 to stop)
2025-06-08 14:03:41,950 - src.SpeechRecognitionManager - INFO - Listening for speech...
2025-06-08 14:03:44,737 - src.SpeechRecognitionManager - INFO - Stopped recording
2025-06-08 14:04:37,407 - src.VoiceAssistant - INFO - Received question from ('10.5.0.2', 64008): TompTTV says "Did I break you again?"
2025-06-08 14:04:37,407 - src.VoiceAssistant - INFO - Processing question: TompTTV says "Did I break you again?"
2025-06-08 14:04:41,142 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-06-08 14:04:41,142 - src.VoiceAssistant - INFO - AI response generated for question: TompTTV says "Did I break you again?"
2025-06-08 14:04:42,345 - src.tts_manager - INFO - PyAudio instance initialized for optimized playback
2025-06-08 14:04:42,347 - src.OBSWebsocketsManager - ERROR - Failed to set source visibility: 'sceneItemId'
2025-06-08 14:04:42,347 - src.tts_manager - WARNING - Failed to set OBS visibility: 'sceneItemId'
2025-06-08 14:04:42,349 - src.tts_manager - ERROR - Error playing audio: [Errno 13] Permission denied: 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp88zo8q_4.wav'
2025-06-08 14:04:42,350 - src.OBSWebsocketsManager - ERROR - Failed to set source visibility: 'sceneItemId'
2025-06-08 14:04:42,350 - src.tts_manager - WARNING - Failed to hide character in OBS: 'sceneItemId'
2025-06-08 14:05:03,039 - src.VoiceAssistant - INFO - Received question from ('10.5.0.2', 56994): TompTTV says "Hello?"
2025-06-08 14:05:03,039 - src.VoiceAssistant - INFO - Processing question: TompTTV says "Hello?"
2025-06-08 14:05:05,343 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-06-08 14:05:05,344 - src.VoiceAssistant - INFO - AI response generated for question: TompTTV says "Hello?"
2025-06-08 14:05:06,757 - src.tts_manager - INFO - PyAudio instance initialized for optimized playback
2025-06-08 14:05:06,758 - src.OBSWebsocketsManager - ERROR - Failed to set source visibility: 'sceneItemId'
2025-06-08 14:05:06,758 - src.tts_manager - WARNING - Failed to set OBS visibility: 'sceneItemId'
2025-06-08 14:05:06,759 - src.tts_manager - ERROR - Error playing audio: [Errno 13] Permission denied: 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp8i8u2m71.wav'
2025-06-08 14:05:06,760 - src.OBSWebsocketsManager - ERROR - Failed to set source visibility: 'sceneItemId'
2025-06-08 14:05:06,760 - src.tts_manager - WARNING - Failed to hide character in OBS: 'sceneItemId'
2025-06-08 14:05:13,426 - src.SpeechRecognitionManager - INFO - Stopped listening for keyboard shortcuts
2025-06-08 14:05:13,427 - src.VoiceAssistant - INFO - Speech recognition stopped
2025-06-08 14:05:13,427 - src.VoiceAssistant - INFO - UDP socket closed
2025-06-08 14:05:13,427 - obswebsocket.core - INFO - Disconnecting...
2025-06-08 14:05:13,427 - src.OBSWebsocketsManager - INFO - Disconnected from OBS WebSocket
2025-06-08 14:05:13,427 - src.VoiceAssistant - INFO - OBS connection closed
2025-06-08 14:05:13,429 - src.VoiceAssistant - INFO - Voice Assistant stopped
2025-06-08 14:06:18,696 - src.VoiceAssistant - INFO - Starting with empty conversation history
2025-06-08 14:06:18,697 - src.AiManager - INFO - Initialized AI Manager with model: mistral
2025-06-08 14:06:18,697 - obswebsocket.core - INFO - Connecting to ws://localhost:4455...
2025-06-08 14:06:18,701 - obswebsocket.core - INFO - Connected!
2025-06-08 14:06:18,702 - src.OBSWebsocketsManager - INFO - Connected to OBS WebSocket at localhost:4455
2025-06-08 14:06:18,714 - src.tts_manager - INFO - Google Cloud TTS client initialized successfully
2025-06-08 14:06:18,715 - obswebsocket.core - INFO - Connecting to ws://localhost:4455...
2025-06-08 14:06:18,716 - obswebsocket.core - INFO - Connected!
2025-06-08 14:06:18,716 - src.OBSWebsocketsManager - INFO - Connected to OBS WebSocket at localhost:4455
2025-06-08 14:06:18,716 - src.tts_manager - INFO - OBS WebSocket manager initialized for TTS
2025-06-08 14:06:18,806 - src.tts_manager - INFO - PyAudio instance initialized for optimized playback
2025-06-08 14:06:18,806 - src.SpeechRecognitionManager - INFO - Using default microphone
2025-06-08 14:06:18,815 - src.SpeechRecognitionManager - INFO - Adjusting for ambient noise... Please wait.
2025-06-08 14:06:19,814 - src.SpeechRecognitionManager - INFO - Microphone setup complete
2025-06-08 14:06:19,825 - src.VoiceAssistant - INFO - All managers initialized successfully
2025-06-08 14:06:21,828 - src.VoiceAssistant - INFO - UDP socket bound to port 5005
2025-06-08 14:06:21,830 - src.VoiceAssistant - INFO - Started UDP listener on port 5005
2025-06-08 14:06:21,830 - src.VoiceAssistant - INFO - Started question processing thread
2025-06-08 14:06:21,841 - src.SpeechRecognitionManager - INFO - Started listening for keys: F1 to start, F2 to stop recording
2025-06-08 14:06:21,841 - src.VoiceAssistant - INFO - CoHost.AI started successfully
2025-06-08 14:06:24,345 - src.SpeechRecognitionManager - INFO - Started recording (press F2 to stop)
2025-06-08 14:06:24,346 - src.SpeechRecognitionManager - INFO - Listening for speech...
2025-06-08 14:06:26,704 - src.SpeechRecognitionManager - INFO - Processing speech...
2025-06-08 14:06:26,777 - src.SpeechRecognitionManager - INFO - Stopped recording
2025-06-08 14:06:27,354 - src.SpeechRecognitionManager - INFO - Recognized speech: hello can you hear me
2025-06-08 14:06:27,354 - src.VoiceAssistant - INFO - Speech recognized: Voice Input: hello can you hear me
2025-06-08 14:06:27,354 - src.VoiceAssistant - INFO - Processing question: Voice Input: hello can you hear me
2025-06-08 14:06:29,712 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-06-08 14:06:29,713 - src.VoiceAssistant - INFO - AI response generated for question: Voice Input: hello can you hear me
2025-06-08 14:06:31,313 - src.tts_manager - INFO - PyAudio instance initialized for optimized playback
2025-06-08 14:06:31,314 - src.OBSWebsocketsManager - ERROR - Failed to set source visibility: 'sceneItemId'
2025-06-08 14:06:31,314 - src.tts_manager - WARNING - Failed to set OBS visibility: 'sceneItemId'
2025-06-08 14:06:31,315 - src.tts_manager - ERROR - Error playing audio: [Errno 13] Permission denied: 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpb3f5ebb1.wav'
2025-06-08 14:06:31,316 - src.OBSWebsocketsManager - ERROR - Failed to set source visibility: 'sceneItemId'
2025-06-08 14:06:31,316 - src.tts_manager - WARNING - Failed to hide character in OBS: 'sceneItemId'
2025-06-08 14:07:05,448 - src.SpeechRecognitionManager - INFO - Stopped listening for keyboard shortcuts
2025-06-08 14:07:05,448 - src.VoiceAssistant - INFO - Speech recognition stopped
2025-06-08 14:07:05,449 - src.VoiceAssistant - INFO - UDP socket closed
2025-06-08 14:07:05,449 - obswebsocket.core - INFO - Disconnecting...
2025-06-08 14:07:05,449 - src.OBSWebsocketsManager - INFO - Disconnected from OBS WebSocket
2025-06-08 14:07:05,449 - src.VoiceAssistant - INFO - OBS connection closed
2025-06-08 14:07:05,451 - src.VoiceAssistant - INFO - CoHost.AI stopped
