@echo off
echo CoHost.AI Directory Migration
echo =============================
echo.
echo This script will help you rename the directory from "Mike 2.0" to "CoHost.AI"
echo to avoid issues with spaces in the directory name.
echo.

REM Get the current directory name
for %%I in (.) do set "current_dir=%%~nxI"

REM Check if we're in the "Mike 2.0" directory
if "%current_dir%"=="Mike 2.0" (
    echo Current directory: %current_dir%
    echo Suggested new name: CoHost.AI
    echo.
    
    set /p confirm="Do you want to rename the directory to 'CoHost.AI'? (y/n): "
    
    if /i "%confirm%"=="y" (
        echo.
        echo Renaming directory...
        cd ..
        ren "Mike 2.0" "CoHost.AI"
        
        if exist "CoHost.AI" (
            echo ✅ Directory successfully renamed to CoHost.AI
            echo.
            echo Please navigate to the new directory:
            echo cd "CoHost.AI"
            echo.
            echo Then run the setup again:
            echo python setup.py
        ) else (
            echo ❌ Failed to rename directory
        )
    ) else (
        echo Directory rename cancelled.
        echo.
        echo Alternative: You can manually rename the directory or use the updated setup.py
        echo which should handle spaces better.
    )
) else (
    echo Current directory: %current_dir%
    echo.
    echo This script is designed to rename "Mike 2.0" to "CoHost.AI"
    echo You appear to be in a different directory.
    echo.
    echo If you're already in the CoHost.AI directory, you can run:
    echo python setup.py
)

echo.
pause
